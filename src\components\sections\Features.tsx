import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function Features() {
  return (
    <section id="features" className="py-20 bg-white relative overflow-hidden">
      {/* Thought Particles Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="particles-container">
          <div className="particle particle-1"></div>
          <div className="particle particle-3"></div>
          <div className="particle particle-5"></div>
          <div className="particle particle-7"></div>
          <div className="particle particle-9"></div>
          <div className="particle particle-11"></div>
        </div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-dora-blue/10 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
            <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
            Powerful AI Capabilities
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-4">
            Everything You Need to Scale
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From intelligent conversations to payment processing - Dora AI handles it all with human-like understanding
          </p>
        </div>

        {/* Feature Grid with Unique Layout */}
        <div className="grid lg:grid-cols-3 gap-8 w-full">
          {/* Main Feature - Larger */}
          <div className="lg:col-span-2">
            <Card className="bg-white border border-dora-blue/20 shadow-md h-full">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-16 h-16 bg-dora-ultramarine rounded-2xl flex items-center justify-center text-white text-2xl">
                    🤖
                  </div>
                  <div>
                    <CardTitle className="text-2xl text-dora-navy">
                      Smart Q&A Agents
                    </CardTitle>
                    <CardDescription className="text-gray-600 text-lg">
                      AI that thinks, learns, and adapts to your business
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="text-gray-700 space-y-4">
                <p className="text-lg leading-relaxed">
                  Build AI agents that understand context, remember conversations, and provide intelligent responses.
                  Our advanced NLP models ensure your customers get accurate, helpful answers every time.
                </p>
                <div className="grid md:grid-cols-2 gap-4 mt-6">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                      <span className="text-dora-ultramarine text-sm">✓</span>
                    </div>
                    <span className="text-sm">Natural Language Understanding</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                      <span className="text-dora-ultramarine text-sm">✓</span>
                    </div>
                    <span className="text-sm">Context Memory</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                      <span className="text-dora-ultramarine text-sm">✓</span>
                    </div>
                    <span className="text-sm">Multi-language Support</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                      <span className="text-dora-ultramarine text-sm">✓</span>
                    </div>
                    <span className="text-sm">Learning from Interactions</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Side Features */}
          <div className="space-y-8">
            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-dora-blue rounded-xl flex items-center justify-center text-white text-xl">
                    💰
                  </div>
                  <CardTitle className="text-lg text-dora-navy">
                    Sales & Payments
                  </CardTitle>
                </div>
                <CardDescription className="text-gray-600">
                  Complete e-commerce automation
                </CardDescription>
              </CardHeader>
              <CardContent className="text-gray-700 text-sm">
                Process orders, handle payments, calculate shipping, and manage inventory - all through conversational AI.
              </CardContent>
            </Card>

            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-dora-navy rounded-xl flex items-center justify-center text-white text-xl">
                    📱
                  </div>
                  <CardTitle className="text-lg text-dora-navy">
                    WhatsApp Integration
                  </CardTitle>
                </div>
                <CardDescription className="text-gray-600">
                  Native WhatsApp Business API
                </CardDescription>
              </CardHeader>
              <CardContent className="text-gray-700 text-sm">
                Seamless integration with WhatsApp Business. Support for media, documents, and rich messaging features.
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Bottom Row Features */}
        <div className="grid md:grid-cols-3 gap-6 mt-12 w-full">
          <Card className="bg-white border border-dora-blue/20 shadow-md">
            <CardHeader>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-12 h-12 bg-dora-ultramarine rounded-xl flex items-center justify-center text-white text-xl">
                  📊
                </div>
                <CardTitle className="text-lg text-dora-navy">
                  Advanced Dashboard
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="text-gray-700 text-sm">
              Real-time analytics, conversation monitoring, and business insights in one powerful dashboard.
            </CardContent>
          </Card>

          <Card className="bg-white border border-dora-blue/20 shadow-md">
            <CardHeader>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-12 h-12 bg-dora-blue rounded-xl flex items-center justify-center text-white text-xl">
                  🔄
                </div>
                <CardTitle className="text-lg text-dora-navy">
                  Human Takeover
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="text-gray-700 text-sm">
              Smart escalation system that knows when to involve human agents for complex situations.
            </CardContent>
          </Card>

          <Card className="bg-white border border-dora-blue/20 shadow-md">
            <CardHeader>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-12 h-12 bg-dora-navy rounded-xl flex items-center justify-center text-white text-xl">
                  📈
                </div>
                <CardTitle className="text-lg text-dora-navy">
                  Analytics & Insights
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="text-gray-700 text-sm">
              AI-powered recommendations and detailed performance metrics to optimize your agents.
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
