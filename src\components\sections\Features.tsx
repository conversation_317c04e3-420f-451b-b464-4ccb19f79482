import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

export function Features() {
  return (
    <section id="features" className="py-32 bg-gradient-to-b from-white via-gray-50/30 to-white relative overflow-hidden">
      {/* Advanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Mesh */}
        <div className="absolute top-0 left-0 w-full h-full opacity-40">
          <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-br from-dora-blue/10 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-tl from-dora-ultramarine/8 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-dora-navy/5 to-transparent rounded-full blur-2xl"></div>
        </div>

        {/* Enhanced Particles */}
        <div className="particles-container opacity-60">
          <div className="particle particle-1"></div>
          <div className="particle particle-3"></div>
          <div className="particle particle-5"></div>
          <div className="particle particle-7"></div>
          <div className="particle particle-9"></div>
          <div className="particle particle-11"></div>
        </div>

        {/* Floating Geometric Elements */}
        <div className="absolute top-1/4 left-10 w-6 h-6 bg-dora-blue/20 rotate-45 animate-bounce delay-1000"></div>
        <div className="absolute bottom-1/3 right-16 w-4 h-4 bg-dora-ultramarine/30 rounded-full animate-ping delay-2000"></div>
        <div className="absolute top-2/3 left-1/4 w-3 h-12 bg-dora-navy/15 animate-pulse delay-1500"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20 space-y-6">
          <div className="inline-flex items-center bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 backdrop-blur-sm border border-dora-blue/20 text-dora-blue px-6 py-3 rounded-2xl text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="w-2 h-2 bg-gradient-to-r from-dora-ultramarine to-dora-blue rounded-full mr-3 animate-pulse"></div>
            <span className="bg-gradient-to-r from-dora-blue to-dora-ultramarine bg-clip-text text-transparent">
              Powerful AI Capabilities
            </span>
            <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">⚡</div>
          </div>

          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black text-dora-navy mb-6 leading-tight">
            Everything You Need
            <br />
            <span className="bg-gradient-to-r from-dora-ultramarine via-dora-blue to-dora-navy bg-clip-text text-transparent">
              to Scale
            </span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
            From <span className="font-semibold text-dora-navy">intelligent conversations</span> to
            <span className="font-semibold text-dora-ultramarine"> payment processing</span> -
            Dora AI handles it all with <span className="italic">human-like understanding</span>
          </p>
        </div>

        {/* Advanced Feature Grid - Asymmetric Layout */}
        <div className="grid lg:grid-cols-12 gap-8 w-full">
          {/* Hero Feature Card - Spans 8 columns */}
          <div className="lg:col-span-8">
            <div className="group relative">
              {/* Background Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/20 to-dora-ultramarine/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

              {/* Main Card */}
              <Card className="relative bg-white/80 backdrop-blur-xl border border-white/40 shadow-2xl h-full rounded-3xl overflow-hidden group-hover:shadow-dora-blue/10 group-hover:shadow-3xl transition-all duration-500 group-hover:scale-[1.02]">
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/5 via-transparent to-dora-ultramarine/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <CardHeader className="pb-6 relative z-10">
                  <div className="flex items-start gap-6 mb-6">
                    {/* Enhanced Icon */}
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine to-dora-blue rounded-3xl blur-lg opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
                      <div className="relative w-20 h-20 bg-gradient-to-br from-dora-ultramarine via-dora-blue to-dora-navy rounded-3xl flex items-center justify-center text-white text-3xl shadow-2xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                        🤖
                      </div>
                    </div>

                    <div className="flex-1">
                      <CardTitle className="text-3xl md:text-4xl font-black text-dora-navy mb-3 group-hover:text-dora-ultramarine transition-colors duration-300">
                        Smart Q&A Agents
                      </CardTitle>
                      <CardDescription className="text-gray-600 text-xl font-medium leading-relaxed">
                        AI that <span className="font-bold text-dora-ultramarine">thinks</span>,
                        <span className="font-bold text-dora-blue"> learns</span>, and
                        <span className="font-bold text-dora-navy"> adapts</span> to your business
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="text-gray-700 space-y-6 relative z-10">
                  <p className="text-lg md:text-xl leading-relaxed font-light">
                    Build AI agents that understand context, remember conversations, and provide intelligent responses.
                    Our <span className="font-semibold text-dora-navy">advanced NLP models</span> ensure your customers get
                    <span className="font-semibold text-dora-ultramarine"> accurate, helpful answers</span> every time.
                  </p>

                  {/* Enhanced Feature List */}
                  <div className="grid md:grid-cols-2 gap-6 mt-8">
                    {[
                      { icon: "🧠", text: "Natural Language Understanding", color: "from-emerald-400 to-emerald-600" },
                      { icon: "💭", text: "Context Memory", color: "from-blue-400 to-blue-600" },
                      { icon: "🌍", text: "Multi-language Support", color: "from-purple-400 to-purple-600" },
                      { icon: "📈", text: "Learning from Interactions", color: "from-orange-400 to-orange-600" }
                    ].map((feature, index) => (
                      <div key={index} className="flex items-center gap-4 group/item">
                        <div className={`w-12 h-12 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center text-white text-lg shadow-lg group-hover/item:scale-110 transition-transform duration-300`}>
                          {feature.icon}
                        </div>
                        <span className="font-semibold text-gray-700 group-hover/item:text-dora-navy transition-colors duration-300">
                          {feature.text}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Performance Metrics */}
                  <div className="grid grid-cols-3 gap-4 mt-8 p-6 bg-gradient-to-r from-dora-blue/5 to-dora-ultramarine/5 rounded-2xl border border-dora-blue/10">
                    <div className="text-center">
                      <div className="text-2xl font-black text-dora-ultramarine">99.2%</div>
                      <div className="text-xs text-gray-600 font-semibold">Accuracy</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-black text-dora-blue">0.3s</div>
                      <div className="text-xs text-gray-600 font-semibold">Response</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-black text-dora-navy">24/7</div>
                      <div className="text-xs text-gray-600 font-semibold">Available</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Side Features - Vertical Stack */}
          <div className="lg:col-span-4 space-y-8">
            {/* Sales & Payments Card */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-green-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

              <Card className="relative bg-white/90 backdrop-blur-xl border border-white/40 shadow-xl rounded-2xl overflow-hidden group-hover:shadow-emerald-500/10 group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-green-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <CardHeader className="relative z-10">
                  <div className="flex items-center gap-4 mb-3">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 to-green-600 rounded-2xl blur-lg opacity-60"></div>
                      <div className="relative w-16 h-16 bg-gradient-to-br from-emerald-400 to-green-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                        💰
                      </div>
                    </div>
                    <div>
                      <CardTitle className="text-xl font-black text-dora-navy group-hover:text-emerald-600 transition-colors duration-300">
                        Sales & Payments
                      </CardTitle>
                      <CardDescription className="text-gray-600 font-semibold">
                        Complete e-commerce automation
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="text-gray-700 relative z-10">
                  <p className="leading-relaxed mb-4">
                    Process orders, handle payments, calculate shipping, and manage inventory - all through
                    <span className="font-semibold text-emerald-600"> conversational AI</span>.
                  </p>

                  {/* Mini Features */}
                  <div className="space-y-2">
                    {["Payment Processing", "Inventory Management", "Order Tracking"].map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* WhatsApp Integration Card */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

              <Card className="relative bg-white/90 backdrop-blur-xl border border-white/40 shadow-xl rounded-2xl overflow-hidden group-hover:shadow-green-500/10 group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105">
                <div className="absolute inset-0 bg-gradient-to-br from-green-50/50 via-transparent to-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <CardHeader className="relative z-10">
                  <div className="flex items-center gap-4 mb-3">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl blur-lg opacity-60"></div>
                      <div className="relative w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                        📱
                      </div>
                    </div>
                    <div>
                      <CardTitle className="text-xl font-black text-dora-navy group-hover:text-green-600 transition-colors duration-300">
                        WhatsApp Integration
                      </CardTitle>
                      <CardDescription className="text-gray-600 font-semibold">
                        Native WhatsApp Business API
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="text-gray-700 relative z-10">
                  <p className="leading-relaxed mb-4">
                    Seamless integration with WhatsApp Business. Support for
                    <span className="font-semibold text-green-600"> media, documents</span>, and
                    <span className="font-semibold text-emerald-600"> rich messaging</span> features.
                  </p>

                  {/* Mini Features */}
                  <div className="space-y-2">
                    {["Media Support", "Rich Messages", "Business API"].map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Bottom Row Features - Enhanced Grid */}
        <div className="grid md:grid-cols-3 gap-8 mt-16 w-full">
          {/* Advanced Dashboard */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/20 to-purple-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            <Card className="relative bg-white/90 backdrop-blur-xl border border-white/40 shadow-xl rounded-2xl overflow-hidden group-hover:shadow-dora-ultramarine/10 group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/5 via-transparent to-purple-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <CardHeader className="relative z-10">
                <div className="flex items-center gap-4 mb-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine to-purple-600 rounded-2xl blur-lg opacity-60"></div>
                    <div className="relative w-16 h-16 bg-gradient-to-br from-dora-ultramarine to-purple-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                      📊
                    </div>
                  </div>
                  <CardTitle className="text-xl font-black text-dora-navy group-hover:text-dora-ultramarine transition-colors duration-300">
                    Advanced Dashboard
                  </CardTitle>
                </div>
              </CardHeader>

              <CardContent className="text-gray-700 relative z-10">
                <p className="leading-relaxed">
                  <span className="font-semibold text-dora-ultramarine">Real-time analytics</span>, conversation monitoring, and
                  <span className="font-semibold text-purple-600"> business insights</span> in one powerful dashboard.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Human Takeover */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/20 to-cyan-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            <Card className="relative bg-white/90 backdrop-blur-xl border border-white/40 shadow-xl rounded-2xl overflow-hidden group-hover:shadow-dora-blue/10 group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/5 via-transparent to-cyan-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <CardHeader className="relative z-10">
                <div className="flex items-center gap-4 mb-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-dora-blue to-cyan-600 rounded-2xl blur-lg opacity-60"></div>
                    <div className="relative w-16 h-16 bg-gradient-to-br from-dora-blue to-cyan-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                      🔄
                    </div>
                  </div>
                  <CardTitle className="text-xl font-black text-dora-navy group-hover:text-dora-blue transition-colors duration-300">
                    Human Takeover
                  </CardTitle>
                </div>
              </CardHeader>

              <CardContent className="text-gray-700 relative z-10">
                <p className="leading-relaxed">
                  <span className="font-semibold text-dora-blue">Smart escalation system</span> that knows when to involve
                  <span className="font-semibold text-cyan-600"> human agents</span> for complex situations.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Analytics & Insights */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-dora-navy/20 to-indigo-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            <Card className="relative bg-white/90 backdrop-blur-xl border border-white/40 shadow-xl rounded-2xl overflow-hidden group-hover:shadow-dora-navy/10 group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-dora-navy/5 via-transparent to-indigo-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <CardHeader className="relative z-10">
                <div className="flex items-center gap-4 mb-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-dora-navy to-indigo-600 rounded-2xl blur-lg opacity-60"></div>
                    <div className="relative w-16 h-16 bg-gradient-to-br from-dora-navy to-indigo-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                      📈
                    </div>
                  </div>
                  <CardTitle className="text-xl font-black text-dora-navy group-hover:text-dora-navy transition-colors duration-300">
                    Analytics & Insights
                  </CardTitle>
                </div>
              </CardHeader>

              <CardContent className="text-gray-700 relative z-10">
                <p className="leading-relaxed">
                  <span className="font-semibold text-dora-navy">AI-powered recommendations</span> and detailed
                  <span className="font-semibold text-indigo-600"> performance metrics</span> to optimize your agents.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
