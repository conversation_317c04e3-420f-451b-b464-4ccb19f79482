import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function Testimonials() {
  return (
    <section className="py-32 bg-gradient-to-b from-gray-50/50 via-white to-gray-50/30 relative overflow-hidden">
      {/* Advanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Mesh Background */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-10 left-10 w-96 h-96 bg-gradient-to-br from-dora-blue/15 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-tl from-dora-ultramarine/12 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-dora-navy/8 to-transparent rounded-full blur-2xl"></div>
        </div>

        {/* Enhanced Particles */}
        <div className="particles-container opacity-40">
          <div className="particle particle-2"></div>
          <div className="particle particle-4"></div>
          <div className="particle particle-6"></div>
          <div className="particle particle-8"></div>
          <div className="particle particle-10"></div>
          <div className="particle particle-12"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-1/4 right-20 w-5 h-5 bg-dora-blue/25 rotate-45 animate-bounce delay-700"></div>
        <div className="absolute bottom-1/4 left-20 w-3 h-3 bg-dora-ultramarine/35 rounded-full animate-ping delay-1200"></div>
        <div className="absolute top-2/3 right-1/4 w-2 h-10 bg-dora-navy/20 animate-pulse delay-900"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20 space-y-6">
          <div className="inline-flex items-center bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 backdrop-blur-sm border border-dora-blue/20 text-dora-blue px-6 py-3 rounded-2xl text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="w-2 h-2 bg-gradient-to-r from-dora-ultramarine to-dora-blue rounded-full mr-3 animate-pulse"></div>
            <span className="bg-gradient-to-r from-dora-blue to-dora-ultramarine bg-clip-text text-transparent">
              Customer Success Stories
            </span>
            <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">⭐</div>
          </div>

          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black text-dora-navy mb-6 leading-tight">
            Trusted by
            <br />
            <span className="bg-gradient-to-r from-dora-ultramarine via-dora-blue to-dora-navy bg-clip-text text-transparent">
              Growing Businesses
            </span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
            From <span className="font-semibold text-dora-navy">small startups</span> to
            <span className="font-semibold text-dora-ultramarine"> enterprise companies</span> - see how
            <span className="font-semibold text-dora-blue"> Dora AI transforms</span> customer interactions
          </p>
        </div>

        {/* Enhanced Testimonials Layout */}
        <div className="space-y-16 mb-20">
          {/* Hero Testimonial - Full Width with Glassmorphism */}
          <div className="w-full group relative">
            {/* Background Glow */}
            <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/20 to-dora-blue/20 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-all duration-700"></div>

            <Card className="relative bg-white/80 backdrop-blur-xl border border-white/40 shadow-2xl rounded-3xl overflow-hidden group-hover:shadow-dora-ultramarine/10 group-hover:shadow-3xl transition-all duration-700">
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/5 via-transparent to-dora-blue/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

              <CardContent className="p-16 text-center relative z-10">
                <div className="max-w-5xl mx-auto space-y-8">
                  {/* Enhanced Avatar */}
                  <div className="relative mx-auto w-fit">
                    <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine to-dora-blue rounded-full blur-lg opacity-60 scale-125"></div>
                    <div className="relative w-24 h-24 bg-gradient-to-br from-dora-ultramarine via-dora-blue to-dora-navy rounded-full flex items-center justify-center shadow-2xl border-4 border-white/30 group-hover:scale-110 transition-transform duration-500">
                      <span className="text-white font-black text-3xl">TK</span>
                    </div>
                  </div>

                  {/* Enhanced Stars */}
                  <div className="flex justify-center gap-1">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-3xl text-amber-400 animate-pulse" style={{ animationDelay: `${i * 200}ms` }}>
                        ⭐
                      </span>
                    ))}
                  </div>

                  {/* Enhanced Quote */}
                  <blockquote className="text-2xl md:text-3xl lg:text-4xl text-gray-700 leading-relaxed font-light italic relative">
                    <span className="text-6xl text-dora-ultramarine/20 absolute -top-4 -left-4">"</span>
                    <span className="relative z-10">
                      Dora AI <span className="font-semibold text-dora-ultramarine">completely transformed</span> our WhatsApp sales.
                      We went from manually handling <span className="font-semibold text-dora-blue">50 messages a day</span> to automatically processing
                      <span className="font-semibold text-dora-navy"> 500+ orders</span>. The AI understands our products perfectly and even handles
                      <span className="font-semibold text-dora-ultramarine"> complex shipping calculations</span> for international orders.
                    </span>
                    <span className="text-6xl text-dora-ultramarine/20 absolute -bottom-8 -right-4">"</span>
                  </blockquote>

                  {/* Enhanced Attribution */}
                  <div className="flex items-center justify-center gap-4">
                    <h3 className="font-black text-2xl text-dora-navy">Toko Keren</h3>
                    <Badge className="bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 text-dora-blue border border-dora-blue/20 px-4 py-2 text-sm font-bold">
                      E-commerce
                    </Badge>
                  </div>

                  {/* Enhanced Stats */}
                  <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                    {[
                      { value: "300%", label: "Sales Increase", color: "from-emerald-400 to-emerald-600" },
                      { value: "24/7", label: "Availability", color: "from-dora-blue to-dora-ultramarine" },
                      { value: "95%", label: "Automation Rate", color: "from-dora-navy to-indigo-600" }
                    ].map((stat, index) => (
                      <div key={index} className="group/stat relative">
                        <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/20 rounded-2xl blur-lg opacity-0 group-hover/stat:opacity-100 transition-opacity duration-300"></div>
                        <div className="relative bg-gradient-to-br from-white/60 to-white/40 backdrop-blur-xl rounded-2xl p-6 border border-white/30 shadow-xl group-hover/stat:scale-105 transition-transform duration-300">
                          <div className={`text-4xl font-black bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-2`}>
                            {stat.value}
                          </div>
                          <div className="text-sm font-semibold text-gray-600 uppercase tracking-wider">
                            {stat.label}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Secondary Testimonials - Horizontal Layout */}
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardContent className="p-8">
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-14 h-14 bg-dora-blue rounded-xl flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-lg">RS</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-bold text-lg text-dora-navy">Resto Sukses</h4>
                      <Badge className="bg-dora-blue/10 text-dora-blue text-xs">Restaurant</Badge>
                    </div>
                    <div className="text-dora-ultramarine mb-3">★★★★★</div>
                    <p className="text-gray-700 leading-relaxed">
                      "Customers love ordering through WhatsApp. The AI handles reservations, menu questions, and special dietary requests perfectly."
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardContent className="p-8">
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-14 h-14 bg-dora-navy rounded-xl flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-lg">CS</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-bold text-lg text-dora-navy">Clinic Sehat</h4>
                      <Badge className="bg-dora-blue/10 text-dora-blue text-xs">Healthcare</Badge>
                    </div>
                    <div className="text-dora-ultramarine mb-3">★★★★★</div>
                    <p className="text-gray-700 leading-relaxed">
                      "Appointment booking is now automated. The human takeover feature works seamlessly for complex medical consultations."
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Stats Row */}
        <div className="grid md:grid-cols-4 gap-6 bg-white rounded-2xl p-8 border border-dora-blue/20">
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-ultramarine mb-2">10,000+</div>
            <div className="text-gray-600 text-sm">Active AI Agents</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-blue mb-2">2M+</div>
            <div className="text-gray-600 text-sm">Messages Processed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-navy mb-2">500+</div>
            <div className="text-gray-600 text-sm">Happy Businesses</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-ultramarine mb-2">99.9%</div>
            <div className="text-gray-600 text-sm">Uptime</div>
          </div>
        </div>
      </div>
    </section>
  )
}
