import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function Testimonials() {
  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Thought Particles Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="particles-container">
          <div className="particle particle-2"></div>
          <div className="particle particle-4"></div>
          <div className="particle particle-6"></div>
          <div className="particle particle-8"></div>
          <div className="particle particle-10"></div>
          <div className="particle particle-12"></div>
        </div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-dora-blue/5 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
            <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
            Success Stories
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-6">
            Trusted by Growing
            <br />
            <span className="text-dora-ultramarine">
              Businesses Worldwide
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From small startups to enterprise companies - see how Dora AI transforms customer interactions
          </p>
        </div>

        {/* Testimonials Vertical Layout */}
        <div className="space-y-12 mb-16">
          {/* Featured Testimonial - Full Width */}
          <div className="w-full">
            <Card className="bg-white border border-dora-blue/20 shadow-lg">
              <CardContent className="p-12 text-center">
                <div className="max-w-4xl mx-auto">
                  <div className="w-20 h-20 bg-dora-ultramarine rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-white font-bold text-2xl">TK</span>
                  </div>
                  <div className="text-dora-ultramarine text-2xl mb-4">★★★★★</div>
                  <blockquote className="text-2xl text-gray-700 leading-relaxed mb-8 italic">
                    "Dora AI completely transformed our WhatsApp sales. We went from manually handling 50 messages a day to automatically processing 500+ orders.
                    The AI understands our products perfectly and even handles complex shipping calculations for international orders."
                  </blockquote>
                  <div className="flex items-center justify-center gap-2 mb-8">
                    <h3 className="font-bold text-xl text-dora-navy">Toko Keren</h3>
                    <Badge className="bg-dora-blue/10 text-dora-blue">E-commerce</Badge>
                  </div>
                  <div className="grid md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                    <div className="bg-dora-blue/5 rounded-xl p-4">
                      <div className="text-3xl font-bold text-dora-ultramarine mb-1">300%</div>
                      <div className="text-sm text-gray-600">Sales Increase</div>
                    </div>
                    <div className="bg-dora-blue/5 rounded-xl p-4">
                      <div className="text-3xl font-bold text-dora-blue mb-1">24/7</div>
                      <div className="text-sm text-gray-600">Availability</div>
                    </div>
                    <div className="bg-dora-blue/5 rounded-xl p-4">
                      <div className="text-3xl font-bold text-dora-navy mb-1">95%</div>
                      <div className="text-sm text-gray-600">Automation Rate</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Secondary Testimonials - Horizontal Layout */}
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardContent className="p-8">
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-14 h-14 bg-dora-blue rounded-xl flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-lg">RS</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-bold text-lg text-dora-navy">Resto Sukses</h4>
                      <Badge className="bg-dora-blue/10 text-dora-blue text-xs">Restaurant</Badge>
                    </div>
                    <div className="text-dora-ultramarine mb-3">★★★★★</div>
                    <p className="text-gray-700 leading-relaxed">
                      "Customers love ordering through WhatsApp. The AI handles reservations, menu questions, and special dietary requests perfectly."
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardContent className="p-8">
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-14 h-14 bg-dora-navy rounded-xl flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-lg">CS</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-bold text-lg text-dora-navy">Clinic Sehat</h4>
                      <Badge className="bg-dora-blue/10 text-dora-blue text-xs">Healthcare</Badge>
                    </div>
                    <div className="text-dora-ultramarine mb-3">★★★★★</div>
                    <p className="text-gray-700 leading-relaxed">
                      "Appointment booking is now automated. The human takeover feature works seamlessly for complex medical consultations."
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Stats Row */}
        <div className="grid md:grid-cols-4 gap-6 bg-white rounded-2xl p-8 border border-dora-blue/20">
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-ultramarine mb-2">10,000+</div>
            <div className="text-gray-600 text-sm">Active AI Agents</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-blue mb-2">2M+</div>
            <div className="text-gray-600 text-sm">Messages Processed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-navy mb-2">500+</div>
            <div className="text-gray-600 text-sm">Happy Businesses</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-dora-ultramarine mb-2">99.9%</div>
            <div className="text-gray-600 text-sm">Uptime</div>
          </div>
        </div>
      </div>
    </section>
  )
}
