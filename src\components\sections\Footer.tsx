export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-dora-navy via-slate-900 to-dora-navy text-white py-20 relative overflow-hidden">
      {/* Advanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Mesh */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-dora-blue/30 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-tl from-dora-ultramarine/25 to-transparent rounded-full blur-3xl"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-1/4 right-16 w-6 h-6 bg-dora-blue/20 rotate-45 animate-bounce delay-1000"></div>
        <div className="absolute bottom-1/3 left-20 w-4 h-4 bg-dora-ultramarine/25 rounded-full animate-ping delay-1500"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        <div className="grid md:grid-cols-4 gap-12 mb-16">
          {/* Enhanced Logo Section */}
          <div className="md:col-span-1">
            <div className="flex items-center mb-6 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/20 to-dora-blue/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <img
                  src="/logo.png"
                  alt="Dora AI"
                  className="relative h-12 w-auto group-hover:scale-110 transition-transform duration-300 drop-shadow-lg"
                />
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed text-lg font-light mb-6">
              Build <span className="font-semibold text-white">powerful AI agents</span> for any business.
              Automate customer interactions and <span className="font-semibold text-dora-blue">boost sales</span> with
              WhatsApp integration.
            </p>

            {/* Newsletter Signup */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
              <h4 className="text-white font-bold mb-2">Stay Updated</h4>
              <p className="text-gray-400 text-sm mb-3">Get the latest AI insights</p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Enter email"
                  className="flex-1 bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white placeholder-gray-400 text-sm focus:outline-none focus:border-dora-blue"
                />
                <button className="bg-gradient-to-r from-dora-ultramarine to-dora-blue hover:from-dora-blue hover:to-dora-ultramarine px-4 py-2 rounded-xl text-white font-bold text-sm transition-all duration-300">
                  →
                </button>
              </div>
            </div>
          </div>

          {/* Enhanced Navigation Sections */}
          <div>
            <h3 className="text-xl font-black text-white mb-6 relative">
              Product
              <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-dora-ultramarine to-dora-blue"></div>
            </h3>
            <ul className="space-y-4">
              {[
                { href: "#features", text: "Features" },
                { href: "#pricing", text: "Pricing" },
                { href: "#", text: "API" },
                { href: "#", text: "Documentation" }
              ].map((item, index) => (
                <li key={index}>
                  <a
                    href={item.href}
                    className="text-gray-300 hover:text-white transition-all duration-300 font-medium group flex items-center"
                  >
                    <span className="w-0 h-0.5 bg-dora-blue group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                    {item.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-black text-white mb-6 relative">
              Company
              <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-dora-ultramarine to-dora-blue"></div>
            </h3>
            <ul className="space-y-4">
              {[
                { href: "#", text: "About" },
                { href: "#", text: "Blog" },
                { href: "#", text: "Careers" },
                { href: "#contact", text: "Contact" }
              ].map((item, index) => (
                <li key={index}>
                  <a
                    href={item.href}
                    className="text-gray-300 hover:text-white transition-all duration-300 font-medium group flex items-center"
                  >
                    <span className="w-0 h-0.5 bg-dora-ultramarine group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                    {item.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-black text-white mb-6 relative">
              Support
              <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-dora-ultramarine to-dora-blue"></div>
            </h3>
            <ul className="space-y-4">
              {[
                { href: "#", text: "Help Center" },
                { href: "#", text: "Community" },
                { href: "#", text: "Status" },
                { href: "#", text: "Privacy Policy" }
              ].map((item, index) => (
                <li key={index}>
                  <a
                    href={item.href}
                    className="text-gray-300 hover:text-white transition-all duration-300 font-medium group flex items-center"
                  >
                    <span className="w-0 h-0.5 bg-dora-navy group-hover:w-4 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                    {item.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Enhanced Bottom Section */}
        <div className="border-t border-white/10 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            <div className="text-gray-400 text-sm font-medium">
              © 2024 <span className="text-white font-bold">Dora AI</span>. All rights reserved.
            </div>

            {/* Enhanced Social Links */}
            <div className="flex space-x-4">
              {[
                { href: "#", icon: "🐦", label: "Twitter", color: "hover:text-blue-400" },
                { href: "#", icon: "💼", label: "LinkedIn", color: "hover:text-blue-600" },
                { href: "#", icon: "🐙", label: "GitHub", color: "hover:text-gray-300" }
              ].map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className={`w-12 h-12 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl flex items-center justify-center text-gray-400 ${social.color} transition-all duration-300 hover:scale-110 hover:bg-white/10 group`}
                >
                  <span className="sr-only">{social.label}</span>
                  <span className="text-xl group-hover:scale-110 transition-transform duration-300">
                    {social.icon}
                  </span>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
