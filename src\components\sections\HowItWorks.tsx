import { Button } from "@/components/ui/button"

export function HowItWorks() {
  return (
    <section id="how-it-works" className="py-32 bg-gradient-to-b from-gray-50/50 via-white to-gray-50/30 relative overflow-hidden">
      {/* Advanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Mesh */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-32 left-32 w-80 h-80 bg-gradient-to-br from-dora-blue/15 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-32 right-32 w-96 h-96 bg-gradient-to-tl from-dora-ultramarine/12 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-dora-navy/10 to-transparent rounded-full blur-2xl"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-1/4 right-20 w-8 h-8 bg-dora-blue/20 rotate-45 animate-bounce delay-700"></div>
        <div className="absolute bottom-1/4 left-16 w-6 h-6 bg-dora-ultramarine/25 rounded-full animate-ping delay-1200"></div>
        <div className="absolute top-3/4 right-1/4 w-4 h-16 bg-dora-navy/15 animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20 space-y-6">
          <div className="inline-flex items-center bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 backdrop-blur-sm border border-dora-blue/20 text-dora-blue px-6 py-3 rounded-2xl text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="w-2 h-2 bg-gradient-to-r from-dora-ultramarine to-dora-blue rounded-full mr-3 animate-pulse"></div>
            <span className="bg-gradient-to-r from-dora-blue to-dora-ultramarine bg-clip-text text-transparent">
              Simple 3-Step Process
            </span>
            <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">⚡</div>
          </div>

          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black text-dora-navy mb-6 leading-tight">
            From Idea to AI Agent
            <br />
            <span className="bg-gradient-to-r from-dora-ultramarine via-dora-blue to-dora-navy bg-clip-text text-transparent">
              in Minutes
            </span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
            <span className="font-semibold text-dora-navy">No coding required</span>. No complex setup.
            Just <span className="font-semibold text-dora-ultramarine">intelligent AI agents</span> ready to
            <span className="font-semibold text-dora-blue"> transform your business</span>.
          </p>
        </div>

        {/* Enhanced Process Flow */}
        <div className="relative max-w-7xl mx-auto">
          {/* Connection Lines */}
          <div className="hidden lg:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-0.5 bg-gradient-to-r from-dora-blue via-dora-ultramarine to-dora-navy opacity-20"></div>

          <div className="grid lg:grid-cols-3 gap-12 lg:gap-8">
            {/* Step 1 - Enhanced */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/20 to-dora-ultramarine/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

              <div className="relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 text-center shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/40 group-hover:scale-105">
                <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/5 via-transparent to-dora-ultramarine/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                <div className="relative mb-8">
                  <div className="relative mx-auto w-fit">
                    <div className="absolute inset-0 bg-gradient-to-br from-dora-blue to-dora-ultramarine rounded-3xl blur-lg opacity-60 scale-125"></div>
                    <div className="relative w-24 h-24 bg-gradient-to-br from-dora-blue via-dora-ultramarine to-dora-navy rounded-3xl flex items-center justify-center shadow-2xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                      <span className="text-4xl font-black text-white">1</span>
                    </div>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                </div>

                <h3 className="text-3xl font-black text-dora-navy mb-4 group-hover:text-dora-ultramarine transition-colors duration-300">
                  Create Your Agent
                </h3>
                <p className="text-gray-600 mb-8 leading-relaxed text-lg font-light">
                  Choose from <span className="font-semibold text-dora-blue">pre-built templates</span> or create custom agents.
                  Define personality, knowledge base, and capabilities with our
                  <span className="font-semibold text-dora-ultramarine"> intuitive builder</span>.
                </p>

                <div className="space-y-4">
                  {[
                    { text: "Drag & drop interface", icon: "🎯" },
                    { text: "Pre-built templates", icon: "📋" },
                    { text: "Custom training data", icon: "🧠" }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center justify-center gap-3 text-gray-600 group/item">
                      <div className="w-6 h-6 bg-gradient-to-br from-dora-blue to-dora-ultramarine rounded-full flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                        <span className="text-xs">{feature.icon}</span>
                      </div>
                      <span className="font-medium group-hover/item:text-dora-navy transition-colors duration-300">
                        {feature.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative">
              <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-dora-ultramarine rounded-2xl flex items-center justify-center mx-auto shadow-md">
                    <span className="text-3xl font-bold text-white">2</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-dora-navy mb-4">
                  Connect Channels
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  One-click integration with WhatsApp Business, website chat, and more. Your AI agent goes live instantly across all channels.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>WhatsApp Business API</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Website chat widget</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>More channels coming</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative">
              <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-dora-navy rounded-2xl flex items-center justify-center mx-auto shadow-md">
                    <span className="text-3xl font-bold text-white">3</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">⚡</span>
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-dora-navy mb-4">
                  Scale & Optimize
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Monitor performance, analyze conversations, and continuously improve. AI learns from every interaction to serve customers better.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Real-time analytics</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Continuous learning</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Performance optimization</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="inline-flex items-center gap-4 bg-white rounded-2xl p-6 shadow-md border border-dora-blue/10">
              <div className="text-2xl">⏱️</div>
              <div className="text-left">
                <div className="font-semibold text-gray-900">Ready in 5 minutes</div>
                <div className="text-sm text-gray-600">Average setup time for most businesses</div>
              </div>
              <Button className="bg-dora-ultramarine hover:bg-dora-blue text-white px-6">
                Start Now →
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
