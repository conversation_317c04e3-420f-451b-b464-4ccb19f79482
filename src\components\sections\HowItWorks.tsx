import { Button } from "@/components/ui/button"

export function HowItWorks() {
  return (
    <section id="how-it-works" className="py-20 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-1/4 w-64 h-64 border border-dora-blue rounded-full"></div>
        <div className="absolute bottom-20 right-1/4 w-48 h-48 border border-dora-ultramarine rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-dora-blue/5 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
            <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
            Simple 3-Step Process
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-6">
            From Idea to AI Agent
            <br />
            <span className="text-dora-ultramarine">
              in Minutes
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            No coding required. No complex setup. Just intelligent AI agents ready to transform your business.
          </p>
        </div>

        {/* Process Flow */}
        <div className="relative max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-12 lg:gap-8">
            {/* Step 1 */}
            <div className="relative">
              <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-dora-blue rounded-2xl flex items-center justify-center mx-auto shadow-md">
                    <span className="text-3xl font-bold text-white">1</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-dora-navy mb-4">
                  Create Your Agent
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Choose from pre-built templates or create custom agents. Define personality, knowledge base, and capabilities with our intuitive builder.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Drag & drop interface</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Pre-built templates</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Custom training data</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative">
              <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-dora-ultramarine rounded-2xl flex items-center justify-center mx-auto shadow-md">
                    <span className="text-3xl font-bold text-white">2</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-dora-navy mb-4">
                  Connect Channels
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  One-click integration with WhatsApp Business, website chat, and more. Your AI agent goes live instantly across all channels.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>WhatsApp Business API</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Website chat widget</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>More channels coming</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative">
              <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-dora-navy rounded-2xl flex items-center justify-center mx-auto shadow-md">
                    <span className="text-3xl font-bold text-white">3</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">⚡</span>
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-dora-navy mb-4">
                  Scale & Optimize
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Monitor performance, analyze conversations, and continuously improve. AI learns from every interaction to serve customers better.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Real-time analytics</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Continuous learning</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                    <span>Performance optimization</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="inline-flex items-center gap-4 bg-white rounded-2xl p-6 shadow-md border border-dora-blue/10">
              <div className="text-2xl">⏱️</div>
              <div className="text-left">
                <div className="font-semibold text-gray-900">Ready in 5 minutes</div>
                <div className="text-sm text-gray-600">Average setup time for most businesses</div>
              </div>
              <Button className="bg-dora-ultramarine hover:bg-dora-blue text-white px-6">
                Start Now →
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
