import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export function Pricing() {
  return (
    <section id="pricing" className="py-32 bg-gradient-to-b from-white via-gray-50/30 to-white relative overflow-hidden">
      {/* Advanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Mesh */}
        <div className="absolute inset-0 opacity-40">
          <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-dora-blue/12 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-tl from-dora-ultramarine/10 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-dora-navy/8 to-transparent rounded-full blur-2xl"></div>
        </div>

        {/* Enhanced Particles */}
        <div className="particles-container opacity-50">
          <div className="particle particle-1"></div>
          <div className="particle particle-4"></div>
          <div className="particle particle-7"></div>
          <div className="particle particle-10"></div>
        </div>

        {/* Floating Geometric Elements */}
        <div className="absolute top-1/4 left-16 w-6 h-6 bg-dora-blue/25 rotate-45 animate-bounce delay-1000"></div>
        <div className="absolute bottom-1/3 right-20 w-4 h-4 bg-dora-ultramarine/30 rounded-full animate-ping delay-1500"></div>
        <div className="absolute top-2/3 left-1/4 w-3 h-12 bg-dora-navy/20 animate-pulse delay-800"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20 space-y-6">
          <div className="inline-flex items-center bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 backdrop-blur-sm border border-dora-blue/20 text-dora-blue px-6 py-3 rounded-2xl text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="w-2 h-2 bg-gradient-to-r from-dora-ultramarine to-dora-blue rounded-full mr-3 animate-pulse"></div>
            <span className="bg-gradient-to-r from-dora-blue to-dora-ultramarine bg-clip-text text-transparent">
              Transparent Pricing
            </span>
            <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">💎</div>
          </div>

          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black text-dora-navy mb-6 leading-tight">
            Simple Pricing,
            <br />
            <span className="bg-gradient-to-r from-dora-ultramarine via-dora-blue to-dora-navy bg-clip-text text-transparent">
              Powerful Results
            </span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
            Start <span className="font-semibold text-dora-navy">free</span> and scale as you grow.
            <span className="font-semibold text-dora-ultramarine"> No hidden fees</span>,
            <span className="font-semibold text-dora-blue"> no surprises</span>, no long-term contracts.
          </p>
        </div>

        {/* Enhanced Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {/* Starter Plan */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200/20 to-gray-300/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            <Card className="relative bg-white/90 backdrop-blur-xl border border-white/40 shadow-xl rounded-3xl overflow-hidden group-hover:shadow-gray-500/10 group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 via-transparent to-gray-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <CardHeader className="text-center pb-8 relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">🚀</span>
                </div>
                <CardTitle className="text-3xl font-black text-gray-900 group-hover:text-gray-700 transition-colors duration-300">
                  Starter
                </CardTitle>
                <div className="mt-6">
                  <span className="text-5xl font-black text-gray-900">Free</span>
                  <span className="text-gray-600 text-lg font-semibold">/month</span>
                </div>
              </CardHeader>

              <CardContent className="relative z-10">
                <ul className="space-y-4 mb-8">
                  {[
                    "1 AI Agent",
                    "100 messages/month",
                    "WhatsApp integration",
                    "Basic analytics"
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-700 group/item">
                      <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform duration-300">
                        <span className="text-white text-sm font-bold">✓</span>
                      </div>
                      <span className="font-medium group-hover/item:text-gray-900 transition-colors duration-300">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 text-white font-bold py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group-hover:scale-105">
                  Get Started Free
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Professional Plan - Most Popular */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/20 to-dora-blue/20 rounded-3xl blur-xl opacity-60 group-hover:opacity-100 transition-all duration-500 scale-110"></div>

            <Card className="relative bg-white/95 backdrop-blur-xl border-2 border-dora-ultramarine/30 shadow-2xl rounded-3xl overflow-hidden group-hover:shadow-dora-ultramarine/20 group-hover:shadow-3xl transition-all duration-500 group-hover:scale-110 transform scale-105">
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine/10 via-transparent to-dora-blue/10 opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Most Popular Badge */}
              <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-20">
                <Badge className="bg-gradient-to-r from-dora-ultramarine to-dora-blue text-white px-6 py-2 text-sm font-bold shadow-xl border-2 border-white/30 rounded-2xl">
                  ⭐ Most Popular
                </Badge>
              </div>

              <CardHeader className="text-center pb-8 relative z-10 pt-12">
                <div className="relative mx-auto w-fit mb-4">
                  <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine to-dora-blue rounded-2xl blur-lg opacity-60 scale-125"></div>
                  <div className="relative w-20 h-20 bg-gradient-to-br from-dora-ultramarine via-dora-blue to-dora-navy rounded-2xl flex items-center justify-center shadow-2xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white text-3xl">💎</span>
                  </div>
                </div>
                <CardTitle className="text-3xl font-black text-dora-navy group-hover:text-dora-ultramarine transition-colors duration-300">
                  Professional
                </CardTitle>
                <div className="mt-6">
                  <span className="text-5xl font-black bg-gradient-to-r from-dora-ultramarine to-dora-blue bg-clip-text text-transparent">$49</span>
                  <span className="text-gray-600 text-lg font-semibold">/month</span>
                </div>
              </CardHeader>

              <CardContent className="relative z-10">
                <ul className="space-y-4 mb-8">
                  {[
                    { text: "5 AI Agents", icon: "🤖" },
                    { text: "5,000 messages/month", icon: "💬" },
                    { text: "Payment processing", icon: "💳" },
                    { text: "Human takeover", icon: "🔄" },
                    { text: "Advanced analytics", icon: "📊" }
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-700 group/item">
                      <div className="w-8 h-8 bg-gradient-to-br from-dora-ultramarine to-dora-blue rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform duration-300">
                        <span className="text-white text-sm">{feature.icon}</span>
                      </div>
                      <span className="font-semibold group-hover/item:text-dora-navy transition-colors duration-300">
                        {feature.text}
                      </span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full bg-gradient-to-r from-dora-ultramarine to-dora-blue hover:from-dora-blue hover:to-dora-ultramarine text-white font-bold py-4 rounded-2xl shadow-2xl hover:shadow-dora-ultramarine/25 transition-all duration-300 group-hover:scale-105">
                  Start 14-Day Trial
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-2xl font-bold text-gray-900">
                Enterprise
              </CardTitle>
              <div className="mt-4">
                <span className="text-4xl font-bold text-gray-900">Custom</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-gray-700">
                  <span className="text-green-500 mr-3">✓</span>
                  Unlimited agents
                </li>
                <li className="flex items-center text-gray-700">
                  <span className="text-green-500 mr-3">✓</span>
                  Unlimited messages
                </li>
                <li className="flex items-center text-gray-700">
                  <span className="text-green-500 mr-3">✓</span>
                  Custom integrations
                </li>
                <li className="flex items-center text-gray-700">
                  <span className="text-green-500 mr-3">✓</span>
                  Priority support
                </li>
                <li className="flex items-center text-gray-700">
                  <span className="text-green-500 mr-3">✓</span>
                  Dedicated account manager
                </li>
              </ul>
              <Button variant="outline" className="w-full border-gray-300 text-gray-700 hover:bg-gray-50">
                Contact Sales
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
