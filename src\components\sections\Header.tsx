import { Button } from "@/components/ui/button"

export function Header() {
  return (
    <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg shadow-dora-blue/5">
      <div className="container mx-auto px-4 py-4 max-w-7xl">
        <nav className="flex items-center justify-between">
          {/* Enhanced Logo */}
          <div className="flex items-center group">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-dora-ultramarine to-dora-blue rounded-2xl blur-lg opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
              <div className="relative w-12 h-12 bg-gradient-to-br from-dora-ultramarine via-dora-blue to-dora-navy rounded-2xl flex items-center justify-center shadow-xl border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                <span className="text-white font-black text-xl">D</span>
              </div>
            </div>
            <span className="text-2xl font-black text-dora-navy ml-4 group-hover:text-dora-ultramarine transition-colors duration-300">
              Dora AI
            </span>
          </div>

          {/* Enhanced Navigation */}
          <div className="hidden md:flex items-center space-x-10">
            {[
              { href: "#features", text: "Features" },
              { href: "#how-it-works", text: "How It Works" },
              { href: "#pricing", text: "Pricing" },
              { href: "#contact", text: "Contact" }
            ].map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="relative text-gray-600 hover:text-dora-navy font-semibold transition-all duration-300 group/nav"
              >
                <span className="relative z-10">{item.text}</span>
                <div className="absolute inset-0 bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 rounded-lg opacity-0 group-hover/nav:opacity-100 transition-opacity duration-300 -m-2"></div>
                <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-dora-ultramarine to-dora-blue group-hover/nav:w-full transition-all duration-300"></div>
              </a>
            ))}

            {/* Enhanced CTA Buttons */}
            <Button
              variant="outline"
              className="border-2 border-dora-blue/30 text-dora-blue hover:bg-gradient-to-r hover:from-dora-blue/5 hover:to-dora-ultramarine/5 font-semibold px-6 py-3 rounded-xl transition-all duration-300 ml-4"
            >
              Login
            </Button>
            <Button className="bg-gradient-to-r from-dora-ultramarine to-dora-blue hover:from-dora-blue hover:to-dora-ultramarine text-white font-bold px-8 py-3 rounded-xl shadow-xl hover:shadow-dora-ultramarine/25 transition-all duration-300 transform hover:scale-105">
              Get Started
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button variant="ghost" className="text-dora-navy">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </Button>
          </div>
        </nav>
      </div>
    </header>
  )
}
