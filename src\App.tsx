import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

function App() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b border-dora-blue/10 bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 max-w-7xl">
          <nav className="flex items-center justify-between">
            <div className="flex items-center">
              <img src="/logo.png" alt="Dora AI" className="h-10 w-auto" />
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-dora-navy hover:text-dora-ultramarine transition-colors font-medium">Features</a>
              <a href="#how-it-works" className="text-dora-navy hover:text-dora-ultramarine transition-colors font-medium">How It Works</a>
              <a href="#pricing" className="text-dora-navy hover:text-dora-ultramarine transition-colors font-medium">Pricing</a>
              <a href="#contact" className="text-dora-navy hover:text-dora-ultramarine transition-colors font-medium">Contact</a>
              <Button variant="outline" className="border-dora-blue text-dora-blue hover:bg-dora-blue/5">
                Login
              </Button>
              <Button className="bg-dora-ultramarine hover:bg-dora-blue text-white">
                Get Started
              </Button>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="min-h-screen bg-white relative overflow-hidden flex items-center">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="particles-container">
            <div className="particle particle-1"></div>
            <div className="particle particle-2"></div>
            <div className="particle particle-3"></div>
            <div className="particle particle-4"></div>
            <div className="particle particle-5"></div>
            <div className="particle particle-6"></div>
            <div className="particle particle-7"></div>
            <div className="particle particle-8"></div>
            <div className="particle particle-9"></div>
            <div className="particle particle-10"></div>
            <div className="particle particle-11"></div>
            <div className="particle particle-12"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-20 relative z-10 max-w-7xl">
          <div className="w-full">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <div className="text-left">
                <div className="inline-flex items-center bg-dora-blue/5 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
                  <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
                  AI-Powered Business Automation
                </div>

                <h1 className="text-5xl md:text-6xl font-bold text-dora-navy mb-6 leading-tight">
                  Think Beyond
                  <br />
                  <span className="text-dora-ultramarine">
                    Traditional Chatbots
                  </span>
                </h1>

                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  Dora AI creates intelligent agents that think, learn, and adapt. From WhatsApp sales to complex customer support - your AI workforce that never sleeps.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 mb-12">
                  <Button size="lg" className="bg-dora-ultramarine hover:bg-dora-blue text-white font-semibold px-8">
                    <span className="mr-2">🚀</span>
                    Start Building Free
                  </Button>
                  <Button size="lg" variant="outline" className="border-2 border-dora-blue text-dora-blue hover:bg-dora-blue/5 px-8">
                    <span className="mr-2">▶️</span>
                    Watch Demo
                  </Button>
                </div>

                {/* Trust indicators */}
                <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500">
                  <div className="flex items-center">
                    <span className="text-green-500 mr-1">✓</span>
                    No credit card required
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-1">✓</span>
                    Setup in 5 minutes
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-1">✓</span>
                    24/7 support
                  </div>
                </div>
              </div>

              {/* Right Visual */}
              <div className="relative">
                <div className="relative bg-dora-blue/5 rounded-3xl p-8 shadow-lg">
                  {/* AI Brain Visualization */}
                  <div className="relative h-80 flex items-center justify-center">
                    <div className="absolute inset-0 bg-dora-blue/5 rounded-2xl"></div>

                    {/* Central AI Core */}
                    <div className="relative z-10 w-32 h-32 bg-dora-ultramarine rounded-full flex items-center justify-center shadow-lg">
                      <span className="text-4xl text-white">🧠</span>
                    </div>

                    {/* Orbiting Elements */}
                    <div className="absolute inset-0">
                      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">💬</div>
                      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm">💰</div>
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm">📱</div>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-sm">📊</div>
                    </div>

                    {/* Connecting Lines */}
                    <svg className="absolute inset-0 w-full h-full">
                      <circle cx="50%" cy="50%" r="60" stroke="#0029F8" strokeWidth="2" fill="none" />
                      <circle cx="50%" cy="50%" r="80" stroke="#001363" strokeWidth="1" fill="none" />
                    </svg>
                  </div>

                  {/* Floating Stats */}
                  <div className="absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100">
                    <div className="text-xs text-gray-500 mb-1">Messages Today</div>
                    <div className="text-lg font-bold text-dora-ultramarine">+2,847</div>
                  </div>

                  <div className="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100">
                    <div className="text-xs text-gray-500 mb-1">Response Time</div>
                    <div className="text-lg font-bold text-dora-blue">0.3s</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-1/4 w-64 h-64 border border-dora-blue rounded-full"></div>
          <div className="absolute bottom-20 right-1/4 w-48 h-48 border border-dora-ultramarine rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 max-w-7xl relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-dora-blue/5 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
              Simple 3-Step Process
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-6">
              From Idea to AI Agent
              <br />
              <span className="text-dora-ultramarine">
                in Minutes
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              No coding required. No complex setup. Just intelligent AI agents ready to transform your business.
            </p>
          </div>

          {/* Process Flow */}
          <div className="relative max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-12 lg:gap-8">
              {/* Step 1 */}
              <div className="relative">
                <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-dora-blue rounded-2xl flex items-center justify-center mx-auto shadow-md">
                      <span className="text-3xl font-bold text-white">1</span>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-dora-navy mb-4">
                    Create Your Agent
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Choose from pre-built templates or create custom agents. Define personality, knowledge base, and capabilities with our intuitive builder.
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Drag & drop interface</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Pre-built templates</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Custom training data</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 2 */}
              <div className="relative">
                <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-dora-ultramarine rounded-2xl flex items-center justify-center mx-auto shadow-md">
                      <span className="text-3xl font-bold text-white">2</span>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-dora-navy mb-4">
                    Connect Channels
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    One-click integration with WhatsApp Business, website chat, and more. Your AI agent goes live instantly across all channels.
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>WhatsApp Business API</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Website chat widget</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>More channels coming</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 3 */}
              <div className="relative">
                <div className="bg-white rounded-2xl p-8 text-center shadow-md hover:shadow-lg transition-all duration-300 border border-dora-blue/10">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-dora-navy rounded-2xl flex items-center justify-center mx-auto shadow-md">
                      <span className="text-3xl font-bold text-white">3</span>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-dora-ultramarine rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">⚡</span>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-dora-navy mb-4">
                    Scale & Optimize
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Monitor performance, analyze conversations, and continuously improve. AI learns from every interaction to serve customers better.
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Real-time analytics</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Continuous learning</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <span className="w-2 h-2 bg-dora-ultramarine rounded-full"></span>
                      <span>Performance optimization</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom CTA */}
            <div className="text-center mt-16">
              <div className="inline-flex items-center gap-4 bg-white rounded-2xl p-6 shadow-md border border-dora-blue/10">
                <div className="text-2xl">⏱️</div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">Ready in 5 minutes</div>
                  <div className="text-sm text-gray-600">Average setup time for most businesses</div>
                </div>
                <Button className="bg-dora-ultramarine hover:bg-dora-blue text-white px-6">
                  Start Now →
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white relative overflow-hidden">
        {/* Thought Particles Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="particles-container">
            <div className="particle particle-1"></div>
            <div className="particle particle-3"></div>
            <div className="particle particle-5"></div>
            <div className="particle particle-7"></div>
            <div className="particle particle-9"></div>
            <div className="particle particle-11"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 max-w-7xl relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-dora-blue/10 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
              Powerful AI Capabilities
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-4">
              Everything You Need to Scale
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From intelligent conversations to payment processing - Dora AI handles it all with human-like understanding
            </p>
          </div>

          {/* Feature Grid with Unique Layout */}
          <div className="grid lg:grid-cols-3 gap-8 w-full">
            {/* Main Feature - Larger */}
            <div className="lg:col-span-2">
              <Card className="bg-white border border-dora-blue/20 shadow-md h-full">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-16 h-16 bg-dora-ultramarine rounded-2xl flex items-center justify-center text-white text-2xl">
                      🤖
                    </div>
                    <div>
                      <CardTitle className="text-2xl text-dora-navy">
                        Smart Q&A Agents
                      </CardTitle>
                      <CardDescription className="text-gray-600 text-lg">
                        AI that thinks, learns, and adapts to your business
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="text-gray-700 space-y-4">
                  <p className="text-lg leading-relaxed">
                    Build AI agents that understand context, remember conversations, and provide intelligent responses.
                    Our advanced NLP models ensure your customers get accurate, helpful answers every time.
                  </p>
                  <div className="grid md:grid-cols-2 gap-4 mt-6">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                        <span className="text-dora-ultramarine text-sm">✓</span>
                      </div>
                      <span className="text-sm">Natural Language Understanding</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                        <span className="text-dora-ultramarine text-sm">✓</span>
                      </div>
                      <span className="text-sm">Context Memory</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                        <span className="text-dora-ultramarine text-sm">✓</span>
                      </div>
                      <span className="text-sm">Multi-language Support</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-dora-blue/10 rounded-full flex items-center justify-center">
                        <span className="text-dora-ultramarine text-sm">✓</span>
                      </div>
                      <span className="text-sm">Learning from Interactions</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Side Features */}
            <div className="space-y-8">
              <Card className="bg-white border border-dora-blue/20 shadow-md">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-12 h-12 bg-dora-blue rounded-xl flex items-center justify-center text-white text-xl">
                      💰
                    </div>
                    <CardTitle className="text-lg text-dora-navy">
                      Sales & Payments
                    </CardTitle>
                  </div>
                  <CardDescription className="text-gray-600">
                    Complete e-commerce automation
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-gray-700 text-sm">
                  Process orders, handle payments, calculate shipping, and manage inventory - all through conversational AI.
                </CardContent>
              </Card>

              <Card className="bg-white border border-dora-blue/20 shadow-md">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-12 h-12 bg-dora-navy rounded-xl flex items-center justify-center text-white text-xl">
                      📱
                    </div>
                    <CardTitle className="text-lg text-dora-navy">
                      WhatsApp Integration
                    </CardTitle>
                  </div>
                  <CardDescription className="text-gray-600">
                    Native WhatsApp Business API
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-gray-700 text-sm">
                  Seamless integration with WhatsApp Business. Support for media, documents, and rich messaging features.
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Bottom Row Features */}
          <div className="grid md:grid-cols-3 gap-6 mt-12 w-full">
            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-dora-ultramarine rounded-xl flex items-center justify-center text-white text-xl">
                    📊
                  </div>
                  <CardTitle className="text-lg text-dora-navy">
                    Advanced Dashboard
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-gray-700 text-sm">
                Real-time analytics, conversation monitoring, and business insights in one powerful dashboard.
              </CardContent>
            </Card>

            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-dora-blue rounded-xl flex items-center justify-center text-white text-xl">
                    🔄
                  </div>
                  <CardTitle className="text-lg text-dora-navy">
                    Human Takeover
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-gray-700 text-sm">
                Smart escalation system that knows when to involve human agents for complex situations.
              </CardContent>
            </Card>

            <Card className="bg-white border border-dora-blue/20 shadow-md">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-dora-navy rounded-xl flex items-center justify-center text-white text-xl">
                    📈
                  </div>
                  <CardTitle className="text-lg text-dora-navy">
                    Analytics & Insights
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="text-gray-700 text-sm">
                AI-powered recommendations and detailed performance metrics to optimize your agents.
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        {/* Thought Particles Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="particles-container">
            <div className="particle particle-2"></div>
            <div className="particle particle-4"></div>
            <div className="particle particle-6"></div>
            <div className="particle particle-8"></div>
            <div className="particle particle-10"></div>
            <div className="particle particle-12"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 max-w-7xl relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-dora-blue/5 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
              Success Stories
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-6">
              Trusted by Growing
              <br />
              <span className="text-dora-ultramarine">
                Businesses Worldwide
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From small startups to enterprise companies - see how Dora AI transforms customer interactions
            </p>
          </div>

          {/* Testimonials Vertical Layout */}
          <div className="space-y-12 mb-16">
            {/* Featured Testimonial - Full Width */}
            <div className="w-full">
              <Card className="bg-white border border-dora-blue/20 shadow-lg">
                <CardContent className="p-12 text-center">
                  <div className="max-w-4xl mx-auto">
                    <div className="w-20 h-20 bg-dora-ultramarine rounded-full flex items-center justify-center mx-auto mb-6">
                      <span className="text-white font-bold text-2xl">TK</span>
                    </div>
                    <div className="text-dora-ultramarine text-2xl mb-4">★★★★★</div>
                    <blockquote className="text-2xl text-gray-700 leading-relaxed mb-8 italic">
                      "Dora AI completely transformed our WhatsApp sales. We went from manually handling 50 messages a day to automatically processing 500+ orders.
                      The AI understands our products perfectly and even handles complex shipping calculations for international orders."
                    </blockquote>
                    <div className="flex items-center justify-center gap-2 mb-8">
                      <h3 className="font-bold text-xl text-dora-navy">Toko Keren</h3>
                      <Badge className="bg-dora-blue/10 text-dora-blue">E-commerce</Badge>
                    </div>
                    <div className="grid md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                      <div className="bg-dora-blue/5 rounded-xl p-4">
                        <div className="text-3xl font-bold text-dora-ultramarine mb-1">300%</div>
                        <div className="text-sm text-gray-600">Sales Increase</div>
                      </div>
                      <div className="bg-dora-blue/5 rounded-xl p-4">
                        <div className="text-3xl font-bold text-dora-blue mb-1">24/7</div>
                        <div className="text-sm text-gray-600">Availability</div>
                      </div>
                      <div className="bg-dora-blue/5 rounded-xl p-4">
                        <div className="text-3xl font-bold text-dora-navy mb-1">95%</div>
                        <div className="text-sm text-gray-600">Automation Rate</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Secondary Testimonials - Horizontal Layout */}
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="bg-white border border-dora-blue/20 shadow-md">
                <CardContent className="p-8">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="w-14 h-14 bg-dora-blue rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold text-lg">RS</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-bold text-lg text-dora-navy">Resto Sukses</h4>
                        <Badge className="bg-dora-blue/10 text-dora-blue text-xs">Restaurant</Badge>
                      </div>
                      <div className="text-dora-ultramarine mb-3">★★★★★</div>
                      <p className="text-gray-700 leading-relaxed">
                        "Customers love ordering through WhatsApp. The AI handles reservations, menu questions, and special dietary requests perfectly."
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border border-dora-blue/20 shadow-md">
                <CardContent className="p-8">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="w-14 h-14 bg-dora-navy rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold text-lg">CS</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-bold text-lg text-dora-navy">Clinic Sehat</h4>
                        <Badge className="bg-dora-blue/10 text-dora-blue text-xs">Healthcare</Badge>
                      </div>
                      <div className="text-dora-ultramarine mb-3">★★★★★</div>
                      <p className="text-gray-700 leading-relaxed">
                        "Appointment booking is now automated. The human takeover feature works seamlessly for complex medical consultations."
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Stats Row */}
          <div className="grid md:grid-cols-4 gap-6 bg-white rounded-2xl p-8 border border-dora-blue/20">
            <div className="text-center">
              <div className="text-3xl font-bold text-dora-ultramarine mb-2">10,000+</div>
              <div className="text-gray-600 text-sm">Active AI Agents</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-dora-blue mb-2">2M+</div>
              <div className="text-gray-600 text-sm">Messages Processed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-dora-navy mb-2">500+</div>
              <div className="text-gray-600 text-sm">Happy Businesses</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-dora-ultramarine mb-2">99.9%</div>
              <div className="text-gray-600 text-sm">Uptime</div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-dora-blue/5 relative overflow-hidden">
        {/* Thought Particles Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="particles-container">
            <div className="particle particle-1"></div>
            <div className="particle particle-4"></div>
            <div className="particle particle-7"></div>
            <div className="particle particle-10"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 max-w-7xl relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-dora-blue/10 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
              Transparent Pricing
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-dora-navy mb-6">
              Simple Pricing,
              <br />
              <span className="text-dora-ultramarine">
                Powerful Results
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Start free and scale as you grow. No hidden fees, no surprises, no long-term contracts.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Starter
                </CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">Free</span>
                  <span className="text-gray-600">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    1 AI Agent
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    100 messages/month
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    WhatsApp integration
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Basic analytics
                  </li>
                </ul>
                <Button className="w-full bg-gray-900 hover:bg-gray-800 text-white">
                  Get Started Free
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white border border-dora-blue/30 shadow-lg relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-dora-ultramarine text-white">Most Popular</Badge>
              </div>
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Professional
                </CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">$49</span>
                  <span className="text-gray-600">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    5 AI Agents
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    5,000 messages/month
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Payment processing
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Human takeover
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Advanced analytics
                  </li>
                </ul>
                <Button className="w-full bg-dora-ultramarine hover:bg-dora-blue text-white">
                  Start 14-Day Trial
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Enterprise
                </CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">Custom</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Unlimited agents
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Unlimited messages
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Custom integrations
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Priority support
                  </li>
                  <li className="flex items-center text-gray-700">
                    <span className="text-green-500 mr-3">✓</span>
                    Dedicated account manager
                  </li>
                </ul>
                <Button variant="outline" className="w-full border-gray-300 text-gray-700 hover:bg-gray-50">
                  Contact Sales
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-dora-navy relative overflow-hidden">
        {/* Thought Particles Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="particles-container">
            <div className="particle particle-2"></div>
            <div className="particle particle-5"></div>
            <div className="particle particle-8"></div>
            <div className="particle particle-11"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 max-w-7xl text-center relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-dora-blue/80 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses already using Dora AI to automate their customer interactions and boost sales.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-dora-ultramarine hover:bg-dora-blue text-white font-semibold px-8">
              Start Building Free
            </Button>
            <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-dora-navy px-8">
              Schedule Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-dora-navy text-white py-12">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center mb-4">
                <img src="/logo.png" alt="Dora AI" className="h-10 w-auto" />
              </div>
              <p className="text-dora-blue/80">
                Build powerful AI agents for any business. Automate customer interactions and boost sales with WhatsApp integration.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-dora-blue/80">
                <li><a href="#features" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-dora-blue/80">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-dora-blue/80">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-dora-blue/20 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="text-dora-blue/60 text-sm mb-4 md:mb-0">
              © 2024 Dora AI. All rights reserved.
            </div>
            <div className="flex space-x-6">
              <a href="#" className="text-dora-blue/80 hover:text-white transition-colors">
                <span className="sr-only">Twitter</span>
                🐦
              </a>
              <a href="#" className="text-dora-blue/80 hover:text-white transition-colors">
                <span className="sr-only">LinkedIn</span>
                💼
              </a>
              <a href="#" className="text-dora-blue/80 hover:text-white transition-colors">
                <span className="sr-only">GitHub</span>
                🐙
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
