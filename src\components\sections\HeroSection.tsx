import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="min-h-screen bg-white relative overflow-hidden flex items-center">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="particles-container">
          <div className="particle particle-1"></div>
          <div className="particle particle-2"></div>
          <div className="particle particle-3"></div>
          <div className="particle particle-4"></div>
          <div className="particle particle-5"></div>
          <div className="particle particle-6"></div>
          <div className="particle particle-7"></div>
          <div className="particle particle-8"></div>
          <div className="particle particle-9"></div>
          <div className="particle particle-10"></div>
          <div className="particle particle-11"></div>
          <div className="particle particle-12"></div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10 max-w-7xl">
        <div className="w-full">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-left">
              <div className="inline-flex items-center bg-dora-blue/5 text-dora-blue px-4 py-2 rounded-full text-sm font-medium mb-6">
                <span className="w-2 h-2 bg-dora-ultramarine rounded-full mr-2"></span>
                AI-Powered Business Automation
              </div>

              <h1 className="text-5xl md:text-6xl font-bold text-dora-navy mb-6 leading-tight">
                Think Beyond
                <br />
                <span className="text-dora-ultramarine">
                  Traditional Chatbots
                </span>
              </h1>

              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Dora AI creates intelligent agents that think, learn, and adapt. From WhatsApp sales to complex customer support - your AI workforce that never sleeps.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <Button size="lg" className="bg-dora-ultramarine hover:bg-dora-blue text-white font-semibold px-8">
                  <span className="mr-2">🚀</span>
                  Start Building Free
                </Button>
                <Button size="lg" variant="outline" className="border-2 border-dora-blue text-dora-blue hover:bg-dora-blue/5 px-8">
                  <span className="mr-2">▶️</span>
                  Watch Demo
                </Button>
              </div>

              {/* Trust indicators */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center">
                  <span className="text-green-500 mr-1">✓</span>
                  No credit card required
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-1">✓</span>
                  Setup in 5 minutes
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-1">✓</span>
                  24/7 support
                </div>
              </div>
            </div>

            {/* Right Visual */}
            <div className="relative">
              <div className="relative bg-dora-blue/5 rounded-3xl p-8 shadow-lg">
                {/* AI Brain Visualization */}
                <div className="relative h-80 flex items-center justify-center">
                  <div className="absolute inset-0 bg-dora-blue/5 rounded-2xl"></div>

                  {/* Central AI Core */}
                  <div className="relative z-10 w-32 h-32 bg-dora-ultramarine rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-4xl text-white">🧠</span>
                  </div>

                  {/* Orbiting Elements */}
                  <div className="absolute inset-0">
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">💬</div>
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm">💰</div>
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm">📱</div>
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-sm">📊</div>
                  </div>

                  {/* Connecting Lines */}
                  <svg className="absolute inset-0 w-full h-full">
                    <circle cx="50%" cy="50%" r="60" stroke="#0029F8" strokeWidth="2" fill="none" />
                    <circle cx="50%" cy="50%" r="80" stroke="#001363" strokeWidth="1" fill="none" />
                  </svg>
                </div>

                {/* Floating Stats */}
                <div className="absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100">
                  <div className="text-xs text-gray-500 mb-1">Messages Today</div>
                  <div className="text-lg font-bold text-dora-ultramarine">+2,847</div>
                </div>

                <div className="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100">
                  <div className="text-xs text-gray-500 mb-1">Response Time</div>
                  <div className="text-lg font-bold text-dora-blue">0.3s</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
