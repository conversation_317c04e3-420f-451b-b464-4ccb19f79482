import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="min-h-screen bg-gradient-to-br from-white via-dora-blue/[0.02] to-dora-ultramarine/[0.03] relative overflow-hidden flex items-center">
      {/* Advanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Orbs */}
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-l from-dora-ultramarine/8 to-dora-navy/8 rounded-full blur-3xl animate-pulse delay-1000"></div>

        {/* Floating Geometric Shapes */}
        <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-dora-blue/20 rotate-45 animate-bounce delay-300"></div>
        <div className="absolute top-1/3 right-1/3 w-3 h-3 bg-dora-ultramarine/30 rounded-full animate-ping delay-700"></div>
        <div className="absolute bottom-1/4 left-1/3 w-2 h-8 bg-dora-navy/15 animate-pulse delay-500"></div>

        {/* Enhanced Particles */}
        <div className="particles-container">
          <div className="particle particle-1 opacity-60"></div>
          <div className="particle particle-2 opacity-40"></div>
          <div className="particle particle-3 opacity-70"></div>
          <div className="particle particle-4 opacity-50"></div>
          <div className="particle particle-5 opacity-60"></div>
          <div className="particle particle-6 opacity-45"></div>
          <div className="particle particle-7 opacity-65"></div>
          <div className="particle particle-8 opacity-55"></div>
          <div className="particle particle-9 opacity-50"></div>
          <div className="particle particle-10 opacity-70"></div>
          <div className="particle particle-11 opacity-40"></div>
          <div className="particle particle-12 opacity-60"></div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10 max-w-7xl">
        <div className="w-full">
          <div className="grid lg:grid-cols-12 gap-16 items-center">
            {/* Left Content - Enhanced */}
            <div className="lg:col-span-7 text-left space-y-8">
              {/* Premium Badge */}
              <div className="inline-flex items-center bg-gradient-to-r from-dora-blue/10 to-dora-ultramarine/10 backdrop-blur-sm border border-dora-blue/20 text-dora-blue px-6 py-3 rounded-2xl text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div className="w-2 h-2 bg-gradient-to-r from-dora-ultramarine to-dora-blue rounded-full mr-3 animate-pulse"></div>
                <span className="bg-gradient-to-r from-dora-blue to-dora-ultramarine bg-clip-text text-transparent">
                  AI-Powered Business Automation
                </span>
                <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">✨</div>
              </div>

              {/* Hero Title - Advanced Typography */}
              <div className="space-y-4">
                <h1 className="text-6xl md:text-7xl lg:text-8xl font-black text-dora-navy leading-[0.9] tracking-tight">
                  <span className="block">Think</span>
                  <span className="block bg-gradient-to-r from-dora-ultramarine via-dora-blue to-dora-navy bg-clip-text text-transparent">
                    Beyond
                  </span>
                  <span className="block text-5xl md:text-6xl lg:text-7xl font-light text-gray-600">
                    Traditional Chatbots
                  </span>
                </h1>
              </div>

              {/* Enhanced Description */}
              <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-2xl font-light">
                Dora AI creates <span className="font-semibold text-dora-navy">intelligent agents</span> that think, learn, and adapt.
                From <span className="font-semibold text-dora-ultramarine">WhatsApp sales</span> to complex customer support -
                your AI workforce that <span className="italic">never sleeps</span>.
              </p>

              {/* Premium CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-dora-ultramarine to-dora-blue hover:from-dora-blue hover:to-dora-ultramarine text-white font-bold px-10 py-4 rounded-2xl shadow-2xl hover:shadow-dora-ultramarine/25 transition-all duration-300 transform hover:scale-105 group"
                >
                  <span className="mr-3 text-xl group-hover:animate-bounce">🚀</span>
                  <span className="text-lg">Start Building Free</span>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-dora-blue/30 text-dora-blue hover:bg-gradient-to-r hover:from-dora-blue/5 hover:to-dora-ultramarine/5 px-10 py-4 rounded-2xl backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  <span className="mr-3 text-lg group-hover:scale-110 transition-transform duration-300">▶️</span>
                  <span className="text-lg font-semibold">Watch Demo</span>
                </Button>
              </div>

              {/* Enhanced Trust Indicators */}
              <div className="flex flex-wrap items-center gap-8 pt-4">
                {[
                  { icon: "✓", text: "No credit card required", color: "text-emerald-500" },
                  { icon: "⚡", text: "Setup in 5 minutes", color: "text-amber-500" },
                  { icon: "🛡️", text: "24/7 support", color: "text-blue-500" }
                ].map((item, index) => (
                  <div key={index} className="flex items-center group">
                    <span className={`${item.color} mr-2 text-lg group-hover:scale-110 transition-transform duration-300`}>
                      {item.icon}
                    </span>
                    <span className="text-gray-600 font-medium group-hover:text-dora-navy transition-colors duration-300">
                      {item.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Visual - Completely Redesigned */}
            <div className="lg:col-span-5 relative">
              {/* Main Glassmorphism Container */}
              <div className="relative">
                {/* Background Glow */}
                <div className="absolute inset-0 bg-gradient-to-br from-dora-blue/20 to-dora-ultramarine/20 rounded-[2rem] blur-xl"></div>

                {/* Main Glass Container */}
                <div className="relative bg-white/40 backdrop-blur-xl border border-white/20 rounded-[2rem] p-8 shadow-2xl">
                  {/* AI Visualization Hub */}
                  <div className="relative h-96 flex items-center justify-center">
                    {/* Central AI Core - Enhanced */}
                    <div className="relative z-20">
                      {/* Outer Glow Ring */}
                      <div className="absolute inset-0 bg-gradient-to-r from-dora-ultramarine to-dora-blue rounded-full blur-lg opacity-60 animate-pulse scale-150"></div>

                      {/* Main Core */}
                      <div className="relative w-40 h-40 bg-gradient-to-br from-dora-ultramarine via-dora-blue to-dora-navy rounded-full flex items-center justify-center shadow-2xl border-4 border-white/30">
                        <div className="text-5xl animate-pulse">🧠</div>

                        {/* Inner Glow */}
                        <div className="absolute inset-2 bg-gradient-to-br from-white/20 to-transparent rounded-full"></div>
                      </div>
                    </div>

                    {/* Advanced Orbiting Elements */}
                    <div className="absolute inset-0 animate-spin" style={{ animationDuration: '20s' }}>
                      {/* Chat Element */}
                      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-2xl flex items-center justify-center text-white text-lg shadow-lg border-2 border-white/30 hover:scale-110 transition-transform duration-300">
                        💬
                      </div>

                      {/* Sales Element */}
                      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-violet-400 to-violet-600 rounded-2xl flex items-center justify-center text-white text-lg shadow-lg border-2 border-white/30 hover:scale-110 transition-transform duration-300">
                        💰
                      </div>

                      {/* Mobile Element */}
                      <div className="absolute left-8 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center text-white text-lg shadow-lg border-2 border-white/30 hover:scale-110 transition-transform duration-300">
                        📱
                      </div>

                      {/* Analytics Element */}
                      <div className="absolute right-8 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-gradient-to-br from-rose-400 to-rose-600 rounded-2xl flex items-center justify-center text-white text-lg shadow-lg border-2 border-white/30 hover:scale-110 transition-transform duration-300">
                        📊
                      </div>
                    </div>

                    {/* Connecting Network Lines */}
                    <svg className="absolute inset-0 w-full h-full opacity-30">
                      <defs>
                        <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#0029F8" />
                          <stop offset="100%" stopColor="#001363" />
                        </linearGradient>
                      </defs>
                      <circle cx="50%" cy="50%" r="80" stroke="url(#lineGradient)" strokeWidth="2" fill="none" strokeDasharray="5,5" className="animate-pulse" />
                      <circle cx="50%" cy="50%" r="100" stroke="url(#lineGradient)" strokeWidth="1" fill="none" strokeDasharray="3,3" className="animate-pulse" style={{ animationDelay: '1s' }} />
                      <circle cx="50%" cy="50%" r="120" stroke="url(#lineGradient)" strokeWidth="1" fill="none" strokeDasharray="2,4" className="animate-pulse" style={{ animationDelay: '2s' }} />
                    </svg>
                  </div>
                </div>

                {/* Floating Stats Cards - Enhanced */}
                <div className="absolute -top-6 -right-6 bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl p-4 border border-white/30 hover:scale-105 transition-transform duration-300 group">
                  <div className="text-xs font-semibold text-gray-500 mb-1 uppercase tracking-wider">Messages Today</div>
                  <div className="text-2xl font-black bg-gradient-to-r from-dora-ultramarine to-dora-blue bg-clip-text text-transparent">
                    +2,847
                  </div>
                  <div className="text-xs text-emerald-500 font-semibold">↗ +23%</div>
                </div>

                <div className="absolute -bottom-6 -left-6 bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl p-4 border border-white/30 hover:scale-105 transition-transform duration-300 group">
                  <div className="text-xs font-semibold text-gray-500 mb-1 uppercase tracking-wider">Response Time</div>
                  <div className="text-2xl font-black bg-gradient-to-r from-dora-blue to-dora-navy bg-clip-text text-transparent">
                    0.3s
                  </div>
                  <div className="text-xs text-emerald-500 font-semibold">⚡ Ultra Fast</div>
                </div>

                <div className="absolute top-1/2 -left-8 transform -translate-y-1/2 bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl p-3 border border-white/30 hover:scale-105 transition-transform duration-300">
                  <div className="text-xs font-semibold text-gray-500 mb-1">AI Accuracy</div>
                  <div className="text-lg font-black text-dora-ultramarine">99.2%</div>
                </div>

                <div className="absolute top-1/4 -right-8 bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl p-3 border border-white/30 hover:scale-105 transition-transform duration-300">
                  <div className="text-xs font-semibold text-gray-500 mb-1">Active Agents</div>
                  <div className="text-lg font-black text-dora-navy">1,247</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
